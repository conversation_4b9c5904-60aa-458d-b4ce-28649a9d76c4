import { db } from '@/database/drizzle';
import { documentChunks, documents, documentSpaces, interactions } from '@/database/schema';
import { azure } from '@/lib/azure/openai';
import { searchMaterials } from '@/lib/materials/search';
import { searchDocumentSummaries, searchSummariesByKeywords } from '@/lib/materials/summary-search';
import { DocumentMetadata, RetrievedDocumentChunkDTO } from '@/types/chat';
import type { MaterialSearchResult } from '@/types/material';
import { DocumentType } from '@/types/material';
import type { LangGraphRunnableConfig } from '@langchain/langgraph';
import { generateObject, streamObject } from 'ai';
import { and, desc, eq, inArray, isNotNull, isNull } from 'drizzle-orm';
import { z } from 'zod';
import type { AgentStateType, DocumentSummary, NextAction } from './types';

// 🧪 TESTING CONFIGURATION
// Set to true to use simulated responses and save AI tokens
const TESTING_MODE = false; // true || false

// Helper function to simulate AI delays for realistic testing
const simulateAIDelay = () =>
  new Promise((resolve) => setTimeout(resolve, 500 + Math.random() * 1000));

/**
 * Retrieve recent chat history for context
 */
async function retrieveChatHistory(chatId: string, userId: string): Promise<string> {
  try {
    // Get last 3 rounds (6 messages: 3 user + 3 AI)
    const recentMessages = await db
      .select({
        content: interactions.content,
        senderType: interactions.senderType,
        createdAt: interactions.createdAt,
      })
      .from(interactions)
      .where(
        and(
          eq(interactions.userId, userId),
          eq(interactions.topicId, chatId),
          // Only get user and AI messages, skip system messages
          inArray(interactions.senderType, ['user', 'ai'])
        )
      )
      .orderBy(desc(interactions.createdAt))
      .limit(6); // Last 6 messages = 3 rounds

    if (recentMessages.length === 0) {
      return '';
    }

    // Reverse to get chronological order and format
    const formattedHistory = recentMessages
      .reverse()
      .map((msg) => {
        const truncatedContent =
          msg.content.length > 200 ? msg.content.substring(0, 200) + '...' : msg.content;
        return `${msg.senderType.toUpperCase()}: ${truncatedContent}`;
      })
      .join('\n');

    return `\nRECENT CHAT HISTORY:\n${formattedHistory}\n`;
  } catch (error) {
    console.error('❌ Error retrieving chat history:', error);
    return '';
  }
}

// Schema for query intent classification
const QueryIntentSchema = z.object({
  intent: z
    .enum(['research', 'casual', 'greeting', 'clarification'])
    .describe('Primary intent of the user query'),
  needsResearch: z.boolean().describe('Whether this query requires document search'),
  needSelectedContext: z
    .boolean()
    .describe("Whether this query needs information about user's selected materials/spaces"),
  reasoning: z.string().describe('Explanation for the classification'),
});

// Schema for direct response generation
const DirectResponseSchema = z.object({
  response: z.string().describe('Direct response to the user query'),
  tone: z
    .enum(['friendly', 'helpful', 'apologetic', 'informative'])
    .describe('Appropriate tone for the response'),
});

// Schema for research plan creation
const ResearchPlanSchema = z.object({
  planType: z
    .enum(['direct_summary', 'keyword_search', 'comprehensive_research', 'document_not_found'])
    .describe('Type of research plan based on user intent'),
  targetAction: z
    .enum(['retrieve_summary_context', 'retrieve_context', 'generate_answer'])
    .describe('Next action to execute based on the plan'),
  retrievalStrategy: z
    .enum(['chunks_only', 'summaries_only', 'both_chunks_and_summaries'])
    .nullable()
    .describe('What type of content to retrieve (null for document_not_found cases)'),
  needsKeywordGeneration: z.boolean().describe('Whether keyword generation is needed'),
  reasoning: z.string().describe('Explanation for the research plan decision'),
  estimatedComplexity: z
    .enum(['simple', 'moderate', 'complex'])
    .describe('Complexity level of the query for resource allocation'),
});

// Schema for query analysis with enhanced retrieval strategy
const QueryAnalysisSchema = z.object({
  refinedKeywords: z
    .array(z.string())
    .min(1)
    .max(4)
    .describe(
      '1-4 conservative keywords directly from the query, avoid assumptions about professions or categories'
    ),
  queryIntent: z
    .enum(['factual', 'conceptual', 'comparative', 'procedural'])
    .describe('Type of query'),
  retrievalStrategy: z
    .enum(['chunks_only', 'summaries_only', 'both_chunks_and_summaries'])
    .describe('What type of content to retrieve based on query intent'),
  documentCount: z
    .number()
    .min(3)
    .max(15)
    .describe('AI-determined document count based on query complexity'),
  reasoning: z.string().describe('Explanation for keyword selection and retrieval strategy'),
});

// Schema for material selection
const MaterialSelectionSchema = z.object({
  selectedMaterials: z
    .array(
      z.object({
        id: z.string(),
        fileName: z.string(),
        reason: z.string().describe('Why this material was selected'),
      })
    )
    .min(1)
    .max(3)
    .describe('1-3 most relevant materials to retrieve content from'),
  reasoning: z.string().describe('Overall reasoning for material selection'),
});

// Schema for chat history relevance check
const HistoryRelevanceSchema = z.object({
  needsHistory: z
    .boolean()
    .describe('Whether chat history is needed to understand the current query'),
  reasoning: z.string().describe('Explanation for why history is or is not needed'),
});

// Schema for result evaluation
const EvaluationSchema = z.object({
  hasRelevantInfo: z.boolean().describe('Whether the retrieved information is relevant'),
  informationQuality: z
    .enum(['excellent', 'good', 'poor', 'insufficient'])
    .describe('Quality of information'),
  needsSummaryContext: z.boolean().describe('Whether broader document context is needed'),
  nextAction: z
    .enum(['generate_answer', 'retrieve_summary', 'refine_query', 'fail_gracefully'])
    .describe('Recommended next step'),
  reasoning: z.string().describe('Explanation for the decision'),
});

// Schema for answer generation with citations
const AnswerSchema = z.object({
  answer: z.string().describe('Comprehensive answer to the user query'),
  citations: z
    .array(
      z.object({
        text: z.string().describe('Text that needs citation'),
        sourceIndex: z.number().describe('Index of the source document'),
      })
    )
    .describe('Citations with footnote references'),
});

/**
 * Node 0: Check if chat history is needed for context (optional enhancement)
 */
export async function checkHistoryRelevance(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('🔍 Checking if chat history is needed...');

  // Emit step start event
  config?.writer?.({
    type: 'step_start',
    step: 'check_history_relevance',
    message: 'Analyzing query context needs...',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    // Simple heuristic for testing
    const query = state.currentQuery.toLowerCase();
    const needsHistory =
      query.includes('that') ||
      query.includes('this') ||
      query.includes('previous') ||
      query.includes('earlier');

    console.log(`🧪 TESTING: History needed: ${needsHistory}`);
    return {
      needsHistory,
      historyReasoning: needsHistory
        ? 'TESTING: Query contains reference words'
        : 'TESTING: Query is self-contained',
    };
  }

  try {
    const result = await generateObject({
      model: azure('gpt-4o-mini'),
      schema: HistoryRelevanceSchema,
      prompt: `
        Analyze if this query needs chat history context to be properly understood:

        Query: "${state.currentQuery}"

        HISTORY IS NEEDED when the query:
        - References "previous", "earlier", "before", "above"
        - Asks for clarification or follow-up
        - Is incomplete without prior context
        - **ASKS FOR CHAT HISTORY ITSELF** (e.g., "what did I ask?", "show conversation history")

        HISTORY IS NOT NEEDED when the query:
        - Is a complete, standalone question
        - Contains all necessary context
        - Is a new topic/question
        - Asks about specific documents or topics by name

        EXAMPLES:
          NEEDS HISTORY: "Can you explain that in more detail?"
          NEEDS HISTORY: "What about the previous document?"
          NEEDS HISTORY: "What's my last question?"
          NEEDS HISTORY: "What's the history question in this conversation?"
          NEEDS HISTORY: "What did I ask before?"
          NEEDS HISTORY: "Show me our conversation history"
          NO HISTORY: "Who is Alex Chen?"
          NO HISTORY: "What is machine learning?"
          NO HISTORY: "Summarize the marketing report"

        Does this query need chat history for proper understanding?
      `,
    });

    console.log(`🎯 History needed: ${result.object.needsHistory}`);
    console.log(`💭 Reasoning: ${result.object.reasoning}`);

    // If history is needed, retrieve it
    let chatHistory = '';
    if (result.object.needsHistory && state.config?.chatId && state.config?.userId) {
      console.log('📚 Retrieving chat history...');
      chatHistory = await retrieveChatHistory(state.config.chatId, state.config.userId);
      console.log(`📚 Retrieved ${chatHistory.length} characters of chat history`);
    }

    return {
      needsHistory: result.object.needsHistory,
      historyReasoning: result.object.reasoning,
      chatHistory,
    };
  } catch (error) {
    console.error('❌ Error in history relevance check:', error);
    // Default to not needing history to avoid token costs
    return {
      needsHistory: false,
      historyReasoning: 'Error in analysis - defaulting to no history needed',
    };
  }
}

/**
 * Node 1: Classify query intent to determine if research is needed
 */
export async function classifyQueryIntent(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('🤔 Classifying query intent...');

  // Emit step start event
  config?.writer?.({
    type: 'step_start',
    step: 'classify_query',
    message: 'Understanding your question...',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    // Simulate classification logic
    const isGreeting = /^(hi|hello|hey|good morning|good afternoon)/i.test(state.currentQuery);
    const isCasual = /^(thanks|thank you|ok|okay|yes|no)$/i.test(state.currentQuery);

    if (isGreeting || isCasual) {
      console.log('🧪 TESTING: Classified as casual conversation');
      return {
        queryIntent: 'casual',
        skipResearch: true,
        needSelectedContext: false,
        reasoning: 'TESTING: Casual greeting or simple response detected',
      };
    } else {
      console.log('🧪 TESTING: Classified as research query');
      return {
        queryIntent: 'research',
        skipResearch: false,
        needSelectedContext: false, // Default to false in testing
        reasoning: 'TESTING: Query requires document research',
      };
    }
  }

  try {
    // Build context about selected documents/spaces
    const contextInfo = await buildContextInfo(state);
    const hasSelectedContent =
      state.selectedContext?.documents?.length ||
      state.selectedContext?.spaces?.length ||
      state.config.documentIds?.length ||
      state.config.spaceIds?.length;

    const result = await generateObject({
      model: azure('gpt-4o-mini'),
      schema: QueryIntentSchema,
      prompt: `
        Classify the intent of this user query to determine if document research is needed:
        
        Query: "${state.originalQuery}"
        
        ${contextInfo}
        
        USER CONTEXT: ${hasSelectedContent ? 'User has selected specific documents/spaces' : 'User is searching all documents'}
        
        ENHANCED Classification Guidelines:
        - "research": Questions about specific topics, content, or concepts that require document search
        - "casual": Pure social interaction with no content request (thanks, you're welcome, etc.)
        - "greeting": Simple greetings when combined with NO content questions
        - "clarification": Questions about using the system itself (not document content)

        CRITICAL RULES:
        1. If user has SELECTED DOCUMENTS/SPACES and asks ANY content-related question → ALWAYS "research"
        2. If query contains topic-related keywords (how to, what is, explain, tell me about) → "research"
        3. If query asks about learning, teaching, strategies, methods, concepts → "research"
        4. SPECIAL CASE: If user asks about "selected materials", "selected documents", "what I have selected", "my selections" → "clarification" (system info, not document search)
        5. Only classify as non-research if it's purely social/system questions with NO content intent

        SELECTED CONTEXT DECISION:
        Set needSelectedContext = true ONLY when the query specifically asks about:
        - What materials/documents/spaces the user has selected
        - Current selections or chosen items
        - "What do I have selected?", "Show my selections", "List my materials"
        - Any query that needs to know about the user's current document/space choices

        Set needSelectedContext = false for:
        - Content questions about topics (even if user has selections)
        - Casual conversation, greetings
        - General system questions
        - Research queries about document content
        
        Examples:
        - "Hello, how to teach like a pro?" → research, needSelectedContext: false
        - "What is game theory?" → research, needSelectedContext: false
        - "Hello there" → greeting, needSelectedContext: false
        - "Thanks" → casual, needSelectedContext: false
        - "How do I upload documents?" → clarification, needSelectedContext: false
        - "Hello, what does this document say about X?" → research, needSelectedContext: false
        - "Tell me about teaching methods" → research, needSelectedContext: false
        - "What materials do I have selected?" → clarification, needSelectedContext: true
        - "List my selected documents" → clarification, needSelectedContext: true
        - "Show me what I have selected" → clarification, needSelectedContext: true
        - "What's in my current selection?" → clarification, needSelectedContext: true
        
        Remember: When in doubt about content vs casual, choose RESEARCH to avoid missing important queries.
        Only skip research for clearly non-content interactions.
      `,
    });

    const { intent, needsResearch, needSelectedContext, reasoning } = result.object;

    console.log(
      `🎯 Query classified as: ${intent} (research needed: ${needsResearch}, context needed: ${needSelectedContext})`
    );
    console.log(`📝 Reasoning: ${reasoning}`);

    return {
      queryIntent: intent,
      skipResearch: !needsResearch,
      needSelectedContext: needSelectedContext,
      reasoning: reasoning,
    };
  } catch (error) {
    console.error('❌ Error in query classification:', error);
    // Default to research mode on error to be safe
    return {
      queryIntent: 'research',
      skipResearch: false,
      needSelectedContext: false, // Default to false to avoid unnecessary context
      error: null, // Clear any previous errors
    };
  }
}

/**
 * Node 0.5: Create research plan based on user intent and query analysis
 */
export async function createResearchPlan(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('🧠 Creating research plan...');

  // Emit step start event
  config?.writer?.({
    type: 'step_start',
    step: 'create_research_plan',
    message: 'Planning research strategy...',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    const query = state.currentQuery.toLowerCase();
    let planType: 'direct_summary' | 'keyword_search' | 'comprehensive_research';
    let targetAction: 'retrieve_summary_context' | 'retrieve_context' | 'generate_answer';
    let retrievalStrategy: 'chunks_only' | 'summaries_only' | 'both_chunks_and_summaries';
    let needsKeywordGeneration: boolean;
    let reasoning: string;

    // Simulate intelligent plan creation
    if (
      query.includes('summary') ||
      query.includes('overview') ||
      query.includes('give me a summary')
    ) {
      planType = 'direct_summary';
      targetAction = 'retrieve_summary_context';
      retrievalStrategy = 'summaries_only';
      needsKeywordGeneration = false;
      reasoning = 'TESTING: Direct summary request detected';
    } else if (
      query.includes('compare') ||
      query.includes('difference') ||
      query.includes('analysis')
    ) {
      planType = 'comprehensive_research';
      targetAction = 'retrieve_context';
      retrievalStrategy = 'both_chunks_and_summaries';
      needsKeywordGeneration = true;
      reasoning = 'TESTING: Complex analysis requiring comprehensive research';
    } else {
      planType = 'keyword_search';
      targetAction = 'retrieve_context';
      retrievalStrategy = 'chunks_only';
      needsKeywordGeneration = true;
      reasoning = 'TESTING: Standard keyword-based search';
    }

    console.log(
      `🧪 TESTING: Plan=${planType}, Action=${targetAction}, Strategy=${retrievalStrategy}`
    );

    return {
      planType,
      targetAction,
      retrievalStrategy,
      needsKeywordGeneration,
      reasoning,
      estimatedComplexity: 'moderate',
    };
  }

  try {
    // Build context information for AI awareness - only if needed
    const needsContext = state.needSelectedContext;
    const hasSelectedContent =
      state.selectedContext?.documents?.length ||
      state.selectedContext?.spaces?.length ||
      state.config.documentIds?.length ||
      state.config.spaceIds?.length;

    const contextInfo = needsContext && hasSelectedContent ? await buildContextInfo(state) : '';

    const result = await generateObject({
      model: azure('gpt-4o-mini'),
      schema: ResearchPlanSchema,
      prompt: `
        Analyze the user's query and create an optimal research plan based on available materials:

        Query: "${state.currentQuery}"

        ${contextInfo}

        AVAILABLE MATERIALS:
        ${
          state.targetMaterials?.length
            ? `Selected Materials (${state.targetMaterials.length}):
${state.targetMaterials.map((m) => `- "${m.fileName}" (${m.reason})`).join('\n')}`
            : 'No specific materials selected - will search all available documents'
        }

        MATERIAL SELECTION REASONING: ${state.materialSelectionReasoning || 'Not yet determined'}

        USER CONTEXT: ${hasSelectedContent ? 'User has selected specific documents/spaces' : 'User is searching all documents - ALWAYS try retrieval first'}

        IMPORTANT: Even if no specific materials are pre-selected, the system can still search through ALL user documents.
        Only use DOCUMENT_NOT_FOUND for exact filename requests, not content queries.

        PLAN CREATION GUIDELINES:

        1. **DIRECT_SUMMARY**: Use when user explicitly asks for summaries, overviews, or "what is this about"
           - Target Action: retrieve_summary_context
           - Strategy: summaries_only
           - No keyword generation needed
           - BEST FOR: Overview questions, especially with selected documents

        2. **KEYWORD_SEARCH**: Use for specific factual questions or targeted information requests
           - Target Action: retrieve_context
           - Strategy: chunks_only
           - Requires keyword generation
           - BEST FOR: "Who is X?", "What does Y mean?", specific facts

        3. **COMPREHENSIVE_RESEARCH**: Use for complex analysis, comparisons, or multi-faceted questions
           - Target Action: retrieve_context
           - Strategy: both_chunks_and_summaries
           - Requires keyword generation
           - BEST FOR: Comparisons, complex analysis, multi-document research

        4. **DOCUMENT_NOT_FOUND**: Use ONLY when user asks for a specific document by exact filename that clearly doesn't exist
           - Target Action: generate_answer
           - Strategy: null (no retrieval needed)
           - BEST FOR: "Show me the file named 'exact-filename.pdf'" when that exact file doesn't exist
           - DO NOT USE for content-based queries like "summary of book X" - use DIRECT_SUMMARY instead

        IMPORTANT: For content queries (like "summary of book X"), ALWAYS try retrieval first:
        - "Show me summary of book 'Hell Week'" → DIRECT_SUMMARY (search for Hell Week content)
        - "Overview of machine learning" → DIRECT_SUMMARY (search for ML summaries)
        - Only use DOCUMENT_NOT_FOUND for exact filename requests that fail

        MATERIAL-AWARE DECISION FACTORS:
        - If ≤5 materials available: AI has intelligently pre-selected 1-3 most relevant with summary previews
        - If >5 materials available: System uses keyword/chunk search for performance (no pre-selection)
        - If user selected specific documents (like resumes, profiles): Consider DIRECT_SUMMARY for identity questions
        - If materials are limited/focused: Prefer targeted strategies
        - If query asks about a person and we have their resume/profile: DIRECT_SUMMARY often works better
        - Query complexity and scope
        - Type of information requested (summary vs detailed vs comparative)

        EXAMPLES:
        - "Give me a summary of document X" → direct_summary
        - "Who is Alex Chen?" + selected resume → direct_summary (resume summaries contain identity info)
        - "What is machine learning?" → keyword_search
        - "Compare X and Y approaches" → comprehensive_research
        - "How does this document explain Z?" → keyword_search
        - "Who is [person]?" + no specific docs → keyword_search
        - "Show me overview of book 'Hell Week'" → direct_summary (search for Hell Week content)
        - "Open file named 'report-2024.pdf'" + file doesn't exist → document_not_found

        CONTENT vs FILENAME DISTINCTION:
        - Content queries ("summary of X", "overview of Y") → Always try DIRECT_SUMMARY first
        - Filename queries ("open file X", "show document Y.pdf") → Check exact filename, use document_not_found if missing
        - When in doubt, prefer retrieval over immediate failure

        Choose the most efficient plan that will get the user the best results fastest.
        Consider the available materials when making your decision.
      `,
    });

    const plan = result.object;

    console.log(`🎯 Research plan created: ${plan.planType}`);
    console.log(`📋 Target action: ${plan.targetAction}`);
    console.log(`🔍 Strategy: ${plan.retrievalStrategy}`);
    console.log(`💭 Reasoning: ${plan.reasoning}`);

    return {
      planType: plan.planType,
      targetAction: plan.targetAction,
      retrievalStrategy: plan.retrievalStrategy,
      needsKeywordGeneration: plan.needsKeywordGeneration,
      reasoning: plan.reasoning,
      estimatedComplexity: plan.estimatedComplexity,
    };
  } catch (error) {
    console.error('❌ Error creating research plan:', error);
    // Default to keyword search on error
    return {
      planType: 'keyword_search',
      targetAction: 'retrieve_context',
      retrievalStrategy: 'chunks_only',
      needsKeywordGeneration: true,
      reasoning: 'Fallback plan due to planning error',
      estimatedComplexity: 'moderate',
      error: null,
    };
  }
}

/**
 * Node 0.6: Generate direct response for non-research queries
 */
export async function generateDirectResponse(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('💬 Generating direct response...');

  // Emit step start event
  await config?.writer?.({
    type: 'step_start',
    step: 'generate_direct_response',
    message: 'Generating response...',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    const testResponse = `# 🧪 Testing Markdown Rendering

## Overview
This is a **comprehensive test** of markdown rendering during streaming. The content includes various formatting elements to ensure proper display.

### Key Features Being Tested
- **Bold text** and *italic text*
- Bullet points and numbered lists
- Code snippets and \`inline code\`
- Tables and headers

## Document Analysis Results

Based on your document collection, here are the key findings:

### 1. Content Categories
The documents can be categorized as follows:
- **Academic papers** (45%)
- **Technical documentation** (30%)
- **Research reports** (25%)

### 2. Popular Topics
1. **Machine Learning** - Most frequently referenced
2. **Data Science** - Growing trend in recent uploads
3. **Software Engineering** - Consistent presence across documents

## Code Examples

Here's a sample code snippet:

\`\`\`python
def analyze_documents(collection):
    results = []
    for doc in collection:
        analysis = process_document(doc)
        results.append(analysis)
    return results
\`\`\`

## Summary Table

| Category | Count | Percentage |
|----------|-------|------------|
| Academic | 45    | 45%        |
| Technical| 30    | 30%        |
| Research | 25    | 25%        |

## Next Steps

To get started with your document analysis:

1. **Select documents** from your collection
2. **Choose analysis type** (summary, search, or detailed review)
3. **Review results** and ask follow-up questions

> **Note**: This is test content to verify markdown rendering works correctly during streaming.

Feel free to ask me anything about your documents! 📚`;

    console.log('🧪 TESTING: Using enhanced markdown test content');

    // Stream the response content in chunks
    const words = testResponse.split(' ');
    for (let i = 0; i < words.length; i++) {
      const chunk = words[i] + (i < words.length - 1 ? ' ' : '');

      // Emit content chunk
      config?.writer?.({
        type: 'content_chunk',
        content: chunk,
      });

      // Small delay to simulate realistic streaming
      await new Promise((resolve) => setTimeout(resolve, 50));
    }

    console.log('🧪 TESTING: Generated direct response');
    return {
      finalAnswer: testResponse,
    };
  }

  // REAL IMPLEMENTATION: For production use
  try {
    // Trust the AI's decision about whether this is a history query
    const isHistoryQuery = state.needsHistory;

    // Use AI decision for context inclusion - much more intelligent than keyword detection
    const needsContext = state.needSelectedContext;

    const hasSelectedContent =
      state.selectedContext?.documents?.length ||
      state.selectedContext?.spaces?.length ||
      state.config.documentIds?.length ||
      state.config.spaceIds?.length;

    // Only build context info if AI determined it's needed AND user has selections
    const contextInfo = needsContext && hasSelectedContent ? await buildContextInfo(state) : '';
    const shouldShowSelectionGuidance = needsContext && hasSelectedContent;

    const result = await streamObject({
      model: azure('gpt-4o-mini'),
      schema: DirectResponseSchema,
      prompt: `
        Generate a helpful direct response to this ${state.queryIntent} query:

        Query: "${state.originalQuery}"
        Intent: ${state.queryIntent}
        ${state.chatHistory ? `\nChat History:\n${state.chatHistory}` : ''}

        ${contextInfo}

        Guidelines:
        ${
          isHistoryQuery
            ? `
        - This is a HISTORY QUERY asking about previous conversation
        - Use the chat history above to answer what the user's previous questions were
        - List the recent questions clearly and concisely
        - If no chat history is available, politely explain this is the start of the conversation
        `
            : `
        - For greetings: Be friendly and welcoming, explain what you can help with
        - For casual: Be conversational and helpful, offer to assist with document questions
        - For clarification: Provide helpful information about using the system
        - Keep responses concise but warm
        - Mention that you can help with questions about their documents
        `
        }
        ${
          shouldShowSelectionGuidance
            ? `
        - IMPORTANT: The user is asking about their current selections. Use the SELECTED DOCUMENTS/SPACES CONTEXT above to list their current selections
        - Be specific about which documents/spaces they have selected
        - Format the response clearly with the actual names of selected items
        `
            : ''
        }
      `,
    });

    let fullResponse = '';

    // Stream the AI response in real-time
    for await (const partialObject of result.partialObjectStream) {
      const currentResponse = partialObject.response;
      if (currentResponse) {
        const newContent = currentResponse.slice(fullResponse.length);

        if (newContent) {
          // Emit each new chunk of content
          await config?.writer?.({
            type: 'content_chunk',
            content: newContent,
          });

          fullResponse = currentResponse;
        }
      }
    }

    // Get the final result
    const finalResult = await result.object;

    console.log('✅ Generated direct response with real AI streaming');

    return {
      finalAnswer: finalResult.response,
    };
  } catch (error) {
    console.error('❌ Error generating direct response:', error);

    const fallbackResponse =
      "I'm here to help you with questions about your documents. What would you like to know?";

    // Stream fallback response
    const words = fallbackResponse.split(' ');
    for (let i = 0; i < words.length; i++) {
      const chunk = words[i] + (i < words.length - 1 ? ' ' : '');

      await config?.writer?.({
        type: 'content_chunk',
        content: chunk,
      });

      await new Promise((resolve) => setTimeout(resolve, 50));
    }

    return {
      finalAnswer: fallbackResponse,
      error: 'Failed to generate response, used fallback',
    };
  }
}

/**
 * Helper function to fetch material metadata from database
 */
async function fetchMaterialMetadata(state: AgentStateType) {
  const { documentIds, spaceIds } = state.config;

  try {
    if (documentIds?.length) {
      // Priority 1: If specific documents are selected, use those
      console.log(`🎯 Fetching metadata for ${documentIds.length} specific documents`);
      const materials = await db.query.documents.findMany({
        where: and(inArray(documents.id, documentIds), isNull(documents.deletedAt)),
        columns: {
          id: true,
          fileName: true,
          fileType: true,
          summary: true,
          createdAt: true,
        },
      });

      console.log(`📊 Found ${materials.length} specific documents`);
      return materials;
    } else if (spaceIds?.length) {
      // Priority 2: If spaces are selected, get all documents from those spaces via junction table
      console.log(`🏢 Fetching metadata for documents in ${spaceIds.length} spaces`);

      const materials = await db
        .select({
          id: documents.id,
          fileName: documents.fileName,
          fileType: documents.fileType,
          summary: documents.summary,
          createdAt: documents.createdAt,
        })
        .from(documents)
        .innerJoin(documentSpaces, eq(documents.id, documentSpaces.documentId))
        .where(and(inArray(documentSpaces.spaceId, spaceIds), isNull(documents.deletedAt)));

      console.log(`📊 Found ${materials.length} documents from selected spaces`);
      return materials;
    } else {
      // Priority 3: No specific selection, this shouldn't happen in normal flow
      console.log('⚠️ No specific documents or spaces selected');
      return [];
    }
  } catch (error) {
    console.error('❌ Error fetching material metadata:', error);
    return [];
  }
}

/**
 * Node 1.5: Resolve material context and let AI select which materials to target
 */
export async function resolveMaterialContext(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('🎯 Resolving material context...');

  // Emit step start event
  config?.writer?.({
    type: 'step_start',
    step: 'resolve_material_context',
    message: 'Analyzing available materials...',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    // Simulate material selection based on available context
    const hasSelectedDocs = state.config.documentIds?.length;
    const hasSelectedSpaces = state.config.spaceIds?.length;

    if (hasSelectedDocs) {
      console.log(`🧪 TESTING: User selected ${hasSelectedDocs} specific documents`);
      const mockTargetMaterials = Array.from({ length: hasSelectedDocs }, (_, i) => ({
        id: `doc-${i + 1}`,
        fileName: `Selected Document ${i + 1}.pdf`,
        reason: `TESTING: User-selected document for ${state.retrievalStrategy} retrieval`,
      }));

      return {
        targetMaterials: mockTargetMaterials,
        materialSelectionReasoning: 'TESTING: Using user-selected documents for targeted retrieval',
      };
    } else if (hasSelectedSpaces) {
      console.log(`🧪 TESTING: User selected ${hasSelectedSpaces} spaces`);
      // Simulate finding documents in selected spaces
      const mockTargetMaterials = [
        {
          id: 'space-doc-1',
          fileName: 'Space Document 1.pdf',
          reason: `TESTING: Document from selected space for ${state.retrievalStrategy} retrieval`,
        },
        {
          id: 'space-doc-2',
          fileName: 'Space Document 2.pdf',
          reason: `TESTING: Another document from selected space`,
        },
      ];

      return {
        targetMaterials: mockTargetMaterials,
        materialSelectionReasoning:
          'TESTING: Using documents from user-selected spaces for targeted retrieval',
      };
    } else {
      console.log(
        '🧪 TESTING: No specific materials or spaces selected, will search all available'
      );
      return {
        targetMaterials: [],
        materialSelectionReasoning:
          'TESTING: No specific material selection, searching all available documents',
      };
    }
  }

  try {
    // 1. Fetch material metadata from database
    const materialContext = await fetchMaterialMetadata(state);

    if (materialContext.length === 0) {
      return {
        targetMaterials: [],
        materialSelectionReasoning: 'No materials available for selection',
      };
    }

    // 2. Check if we should use intelligent summary-based selection
    const MATERIAL_THRESHOLD = 5;
    const shouldUseIntelligentSelection = materialContext.length <= MATERIAL_THRESHOLD;

    if (!shouldUseIntelligentSelection) {
      console.log(
        `📊 Found ${materialContext.length} materials (>${MATERIAL_THRESHOLD}) - skipping summary-based selection for performance`
      );
      return {
        targetMaterials: [], // Empty means search all materials with keyword/chunk strategy
        materialSelectionReasoning: `Found ${materialContext.length} materials - using keyword search for better performance instead of summary analysis`,
      };
    }

    console.log(
      `📊 Found ${materialContext.length} materials (≤${MATERIAL_THRESHOLD}) - using intelligent summary-based selection`
    );

    // 3. Let AI decide which materials to target (only for small sets)
    const result = await generateObject({
      model: azure('gpt-4o-mini'),
      schema: MaterialSelectionSchema,
      prompt: `
        INTELLIGENT MATERIAL SELECTION (Small Document Set)

        You are analyzing a small, focused set of ${materialContext.length} documents to select the most relevant ones.
        This targeted approach is used because the document count is manageable (≤${MATERIAL_THRESHOLD}).

        Query: "${state.currentQuery}"
        Retrieval Strategy: ${state.retrievalStrategy}

        Available Materials (with summary previews):
        ${materialContext.map((m) => `- "${m.fileName}" (id: ${m.id})\n  Preview: ${m.summary?.substring(0, 100) || 'No summary available'}...`).join('\n')}

        INTELLIGENT SELECTION INSTRUCTIONS:
        - Select 1-3 MOST RELEVANT materials based on the query and summary previews
        - For identity questions ("Who is X?"): Prioritize resumes, profiles, or biographical documents
        - For technical questions: Prioritize documents with relevant technical content in previews
        - For overview questions: Select documents with comprehensive summaries
        - Quality over quantity: Better to select fewer highly relevant documents
        - Use the summary previews to make informed decisions about relevance

        EXAMPLES:
        - Query: "Who is Alex Chen?" + Resume available → Select the resume (identity info)
        - Query: "What is machine learning?" + ML textbook + Resume → Select ML textbook only
        - Query: "Compare X and Y" + Document about X + Document about Y → Select both

        Based on the query and summary previews, which 1-3 materials are MOST RELEVANT for ${state.retrievalStrategy}?
      `,
    });

    return {
      targetMaterials: result.object.selectedMaterials,
      materialSelectionReasoning: result.object.reasoning,
    };
  } catch (error) {
    console.error('❌ Error in material context resolution:', error);
    return {
      targetMaterials: [],
      materialSelectionReasoning: 'Failed to resolve material context, will search all available',
      error: null, // Clear any previous errors
    };
  }
}

/**
 * Helper function to build enhanced context information for AI awareness
 */
async function buildContextInfo(state: AgentStateType): Promise<string> {
  const { selectedContext, config } = state;
  let contextInfo = '';

  // Add selected documents context with enhanced details
  if (selectedContext?.filterScope === 'documents' && selectedContext.documents?.length) {
    contextInfo += `\nSELECTED DOCUMENTS CONTEXT:
User has specifically selected ${selectedContext.documents.length} documents for this query:
${selectedContext.documents.map((doc) => `- "${doc.fileName}"`).join('\n')}

IMPORTANT: The user wants information from these SPECIFIC documents only. When retrieving summaries or content, focus on these exact materials by name.
Note: Document filenames may not fully reflect content - rely on document text for accurate information.`;
  }

  // Also check config for document IDs (alternative selection method) - fetch actual filenames
  if (config.documentIds?.length && !selectedContext?.documents?.length) {
    try {
      const materials = await fetchMaterialMetadata(state);
      if (materials.length > 0) {
        contextInfo += `\nSELECTED DOCUMENTS CONTEXT:
User has specifically selected ${materials.length} documents for this query:
${materials.map((doc) => `- "${doc.fileName}"`).join('\n')}

IMPORTANT: The user wants information from these SPECIFIC documents only. When retrieving summaries or content, focus on these exact materials by name.`;
      } else {
        contextInfo += `\nSELECTED DOCUMENTS CONTEXT:
User has selected ${config.documentIds.length} documents, but document details could not be retrieved.`;
      }
    } catch (error) {
      console.error('Error fetching material metadata for context:', error);
      contextInfo += `\nSELECTED DOCUMENTS CONTEXT:
User has selected ${config.documentIds.length} documents for this query.`;
    }
  }

  if (selectedContext?.filterScope === 'spaces' && selectedContext.spaces?.length) {
    contextInfo += `\nSELECTED SPACES CONTEXT:
User is searching within ${selectedContext.spaces.length} specific spaces:
${selectedContext.spaces.map((space) => `- "${space.name}"${space.description ? `: ${space.description}` : ''}`).join('\n')}

IMPORTANT: When the user asks for summaries or overviews, they may want information from specific documents within these spaces. Pay attention to document names mentioned in the query.`;
  }

  // Also check config for space IDs
  if (config.spaceIds?.length && !selectedContext?.spaces?.length) {
    contextInfo += `\nCONFIG SPACES CONTEXT:
User has specified ${config.spaceIds.length} space IDs in the query configuration:
${config.spaceIds.map((id) => `- Space ID: ${id}`).join('\n')}`;
  }

  if (selectedContext?.filterScope === 'all') {
    contextInfo += `\nSEARCH SCOPE: All available documents in the user's library
IMPORTANT: If the user mentions specific document names or asks for summaries of particular materials, prioritize those documents in retrieval.`;
  }

  return contextInfo;
}

/**
 * Node 2: Enhanced context retrieval with integrated keyword generation
 */
export async function retrieveContext(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('📄 Starting enhanced context retrieval...');

  // Emit step start event
  config?.writer?.({
    type: 'step_start',
    step: 'retrieve_context',
    message: `Retrieving ${state.retrievalStrategy?.replace('_', ' ') || 'content'}...`,
  });

  // Step 1: Generate keywords if needed
  let keywords = state.refinedKeywords;
  let keywordReasoning = state.reasoning;

  if (state.needsKeywordGeneration && keywords.length === 0) {
    console.log('🔍 Generating keywords for retrieval...');

    if (TESTING_MODE) {
      await simulateAIDelay();

      const query = state.currentQuery.toLowerCase();
      if (query.includes('summary') || query.includes('overview')) {
        keywords = ['overview', 'summary'];
        keywordReasoning = 'TESTING: Summary-focused keywords';
      } else if (query.includes('compare') || query.includes('difference')) {
        keywords = ['comparison', 'analysis', 'difference'];
        keywordReasoning = 'TESTING: Comparison-focused keywords';
      } else {
        keywords = ['information', 'details', 'explanation'];
        keywordReasoning = 'TESTING: General information keywords';
      }

      console.log(`🧪 TESTING: Generated keywords: ${keywords.join(', ')}`);
    } else {
      try {
        // Build context information for AI awareness
        const contextInfo = await buildContextInfo(state);

        const result = await generateObject({
          model: azure('gpt-4o-mini'),
          schema: QueryAnalysisSchema,
          prompt: `
            Generate optimal keywords for document search based on this query:

            Query: "${state.currentQuery}"
            Plan Type: ${state.planType}
            Retrieval Strategy: ${state.retrievalStrategy}

            ${contextInfo}

            ENHANCED KEYWORD EXTRACTION GUIDELINES:
            1. Extract 1-4 CONSERVATIVE keywords that directly relate to the query
            2. AVOID generic terms like: "book", "document", "summary", "file", "text", "information"
            3. AVOID making assumptions about professions or categories not mentioned in the query
            4. For person name queries: Use ONLY the exact name(s) mentioned, no assumed professions
            5. Use compound phrases when appropriate (e.g., "game theory" not "game" + "theory")
            6. **MATERIAL AWARENESS**: If the user mentions specific document names, prioritize those materials

            EXAMPLES:
            ❌ BAD: "What's the book about game theory?" → ["book", "summary", "game", "theory"]
            ✅ GOOD: "What's the book about game theory?" → ["game theory", "strategic decision making"]
            ❌ BAD: "Who is Alex Chen?" → ["content creator", "social media", "influencer", "YouTube"]
            ✅ GOOD: "Who is Alex Chen?" → ["Alex Chen", "alexchen"]

            DOCUMENT COUNT LOGIC:
            - Simple factual questions: 3-5 documents
            - Conceptual explanations: 5-8 documents
            - Comparative analysis: 8-12 documents
            - Complex research topics: 10-15 documents

            CRITICAL RULES:
            1. Use ONLY words that appear in or are directly related to the user's query
            2. For person names: Use the exact name as written, no professional assumptions
            3. DO NOT invent professions, categories, or contexts not mentioned in the query
            4. If unsure about a person's profession, use only their name
            5. Keep keywords minimal and conservative

            Consider:
            1. What are the core CONCEPTS the user wants to understand?
            2. What TYPE of question is being asked (factual, summary, analysis)?
            3. Are there SPECIFIC MATERIALS mentioned that should be prioritized?
            4. How many documents would provide comprehensive coverage?
          `,
        });

        keywords = result.object.refinedKeywords;
        keywordReasoning = result.object.reasoning;

        console.log(`🎯 Generated keywords: ${keywords.join(', ')}`);
        console.log(`📊 AI-determined document count: ${result.object.documentCount}`);
        console.log(`💭 Reasoning: ${keywordReasoning}`);

        // Track initial keyword generation in history
        const initialKeywordHistory = [
          {
            attempt: 0,
            keywords: keywords,
            strategy: state.retrievalStrategy || 'chunks_only',
          },
        ];

        // Update state with generated keywords and document count
        state = {
          ...state,
          refinedKeywords: keywords,
          reasoning: keywordReasoning,
          documentCount: result.object.documentCount,
          keywordHistory: initialKeywordHistory,
        };
      } catch (error) {
        console.error('❌ Error generating keywords:', error);
        // Fallback to simple keywords
        keywords = ['search', 'query'];
        keywordReasoning = 'Fallback keywords due to generation error';

        // Track fallback keywords in history
        const fallbackKeywordHistory = [
          {
            attempt: 0,
            keywords: keywords,
            strategy: state.retrievalStrategy || 'chunks_only',
          },
        ];

        state = {
          ...state,
          keywordHistory: fallbackKeywordHistory,
        };
      }
    }
  }

  if (TESTING_MODE) {
    await simulateAIDelay();

    // Retrieve real data from database for testing
    try {
      const realChunks = await db
        .select({
          id: documents.id,
          chunkId: documentChunks.id,
          fileName: documents.fileName,
          content: documentChunks.content,
          key: documents.s3Key,
          fileType: documents.fileType,
          metadata: documentChunks.metadata,
        })
        .from(documents)
        .innerJoin(documentChunks, eq(documents.id, documentChunks.documentId))
        .where(isNull(documents.deletedAt))
        .orderBy(documents.fileName, documentChunks.chunkOrder)
        .limit(3);

      const realSummaries = await db
        .select({
          id: documents.id,
          fileName: documents.fileName,
          summary: documents.summary,
          key: documents.s3Key,
          fileType: documents.fileType,
        })
        .from(documents)
        .where(and(isNull(documents.deletedAt), isNotNull(documents.summary)))
        .orderBy(documents.fileName)
        .limit(3);

      // Convert to expected format with testing indicators
      const testChunks: MaterialSearchResult[] = realChunks.map((chunk, idx) => ({
        id: chunk.id,
        content: `🧪 TESTING: Real chunk from database - ${chunk.content.substring(0, 200)}...`,
        similarity: 0.9 - idx * 0.05, // Decreasing similarity scores
        fileName: chunk.fileName,
        chunkId: chunk.chunkId,
        key: chunk.key,
        fileType: chunk.fileType as DocumentType,
        metadata: chunk.metadata as DocumentMetadata,
      }));

      const testSummaries: DocumentSummary[] = realSummaries.map((doc, idx) => ({
        id: doc.id,
        fileName: doc.fileName,
        summary: `🧪 TESTING: Real summary from database - ${doc.summary}`,
        similarity: 0.95 - idx * 0.05, // High similarity for summaries
        key: doc.key,
        fileType: doc.fileType,
      }));

      console.log(
        `🧪 TESTING: Retrieved ${testChunks.length} real chunks and ${testSummaries.length} real summaries from database`
      );

      // Return based on strategy with updated keywords
      let result: Partial<AgentStateType> = {
        refinedKeywords: keywords,
        reasoning: keywordReasoning,
        retrievalAttempts: 1, // Increment retrieval attempts for testing mode too
      };

      switch (state.retrievalStrategy) {
        case 'chunks_only':
          console.log(`🧪 TESTING: Using ${testChunks.length} real chunks only`);
          result = { ...result, retrievedChunks: testChunks, retrievedSummaries: [] };
          break;
        case 'summaries_only':
          console.log(`🧪 TESTING: Using ${testSummaries.length} real summaries only`);
          result = { ...result, retrievedChunks: [], retrievedSummaries: testSummaries };
          break;
        case 'both_chunks_and_summaries':
          console.log(
            `🧪 TESTING: Using ${testChunks.length} real chunks and ${testSummaries.length} real summaries`
          );
          result = { ...result, retrievedChunks: testChunks, retrievedSummaries: testSummaries };
          break;
      }

      return result;
    } catch (error) {
      console.error('❌ Error retrieving real data for testing:', error);

      // Fallback to simple mock data if database fails
      const fallbackResult: Partial<AgentStateType> = {
        retrievedChunks: [
          {
            id: 'fallback-chunk-1',
            content: `🧪 TESTING FALLBACK: Database retrieval failed for ${state.retrievalStrategy} strategy.`,
            similarity: 0.85,
            fileName: 'Fallback Document.pdf',
            chunkId: 1,
            key: 'fallback-key-1',
            fileType: 'pdf' as DocumentType,
            metadata: { pageNumber: 1 },
          },
        ],
        retrievedSummaries: [],
      };

      console.log('🧪 TESTING: Using fallback data due to database error');
      return fallbackResult;
    }
  }

  try {
    const { config, refinedKeywords, currentQuery, retrievalStrategy, targetMaterials } = state;

    // Determine target document IDs
    const targetDocumentIds = targetMaterials?.length
      ? targetMaterials.map((m) => m.id)
      : config.documentIds;

    console.log(`🔍 Executing ${retrievalStrategy} strategy`);
    console.log(
      `🎯 Target materials: ${targetMaterials?.map((m) => m.fileName).join(', ') || 'All available'}`
    );
    console.log(`🔍 Keywords: ${refinedKeywords.join(', ')}`);

    // Execute strategy-based retrieval
    const baseResult = {
      refinedKeywords: keywords,
      reasoning: keywordReasoning,
      retrievalAttempts: 1, // Increment retrieval attempts for any retrieval operation
    };

    switch (retrievalStrategy) {
      case 'chunks_only':
        // Determine search mode based on retry count
        // retryCount 0 = initial attempt (embedding)
        // retryCount 1 = first retry (fulltext)
        // retryCount 2+ = subsequent retries (embedding fallback)
        const searchMode = state.retryCount === 1 ? 'fulltext' : 'embedding';
        console.log(`🔍 Using ${searchMode} search mode (retry: ${state.retryCount})`);

        const chunks = await searchMaterials({
          query: currentQuery,
          mode: searchMode,
          limit: state.documentCount, // Use AI-determined document count
          documentIds: targetDocumentIds,
          spaceIds: config.spaceIds,
          userId: config.userId,
          debug: true,
        });
        return { ...baseResult, retrievedChunks: chunks, retrievedSummaries: [] };

      case 'summaries_only':
        const summaries = await searchDocumentSummaries({
          query: currentQuery,
          limit: Math.min(state.documentCount, 8), // Use AI-determined count, capped at 8 for summaries
          documentIds: targetDocumentIds,
          spaceIds: config.spaceIds,
          userId: config.userId,
          debug: true,
        });
        return { ...baseResult, retrievedChunks: [], retrievedSummaries: summaries };

      case 'both_chunks_and_summaries':
        const [bothChunks, bothSummaries] = await Promise.all([
          searchMaterials({
            query: currentQuery,
            limit: Math.floor(state.documentCount * 0.7), // 70% of AI-determined count for chunks
            documentIds: targetDocumentIds,
            spaceIds: config.spaceIds,
            userId: config.userId,
            debug: true,
          }),
          searchDocumentSummaries({
            query: currentQuery,
            limit: Math.min(Math.floor(state.documentCount * 0.3), 8), // 30% for summaries, capped at 8
            documentIds: targetDocumentIds,
            spaceIds: config.spaceIds,
            userId: config.userId,
            debug: true,
          }),
        ]);
        return { ...baseResult, retrievedChunks: bothChunks, retrievedSummaries: bothSummaries };

      default:
        // Fallback to chunks only
        const fallbackChunks = await searchMaterials({
          query: currentQuery,
          limit: state.documentCount, // Use AI-determined document count
          documentIds: targetDocumentIds,
          spaceIds: config.spaceIds,
          userId: config.userId,
          debug: true,
        });
        return { ...baseResult, retrievedChunks: fallbackChunks, retrievedSummaries: [] };
    }
  } catch (error) {
    console.error('❌ Error in document retrieval:', error);
    return {
      error: 'Failed to retrieve documents',
      evaluationResult: 'failed',
    };
  }
}

/**
 * Node 3: Evaluate retrieval results and decide next action
 */
export async function evaluateResults(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('⚖️ Evaluating retrieval results...');

  // Emit step start event
  await config?.writer?.({
    type: 'step_start',
    step: 'evaluate_results',
    message: 'Evaluating search results...',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    const { retrievedChunks, retrievedSummaries } = state;
    const totalResults = retrievedChunks.length + retrievedSummaries.length;

    if (totalResults === 0) {
      console.log('🧪 TESTING: No results found - marking as failed');
      return {
        evaluationResult: 'failed',
        reasoning: 'TESTING: No results retrieved',
      };
    } else if (totalResults >= 1) {
      console.log(`🧪 TESTING: Found ${totalResults} results - marking as sufficient`);
      return {
        evaluationResult: 'sufficient',
        reasoning: 'TESTING: Sufficient results found for answer generation',
      };
    } else {
      console.log(`🧪 TESTING: Found ${totalResults} result - needs refinement`);
      return {
        evaluationResult: 'need_refinement',
        reasoning: 'TESTING: Limited results, query refinement needed',
      };
    }
  }

  try {
    const {
      retrievedChunks,
      retrievedSummaries,
      currentQuery,
      retryCount,
      maxRetries,
      retrievalStrategy,
      targetMaterials,
      config,
    } = state;

    // Check if we have selected documents but no results - try summary fallback
    const hasSelectedDocuments = targetMaterials?.length || config.documentIds?.length;
    const totalResults = retrievedChunks.length + retrievedSummaries.length;

    if (totalResults === 0 && hasSelectedDocuments && retrievalStrategy === 'chunks_only') {
      console.log('🔄 No chunks found for selected documents - trying summary fallback');
      return {
        evaluationResult: 'need_summary',
        reasoning: 'No chunks found for selected documents, trying document summaries as fallback',
      };
    }

    // Prepare content for evaluation
    const chunkContent = retrievedChunks.map((chunk) => chunk.content).join('\n\n');
    const summaryContent = retrievedSummaries.map((summary) => summary.summary).join('\n\n');

    const result = await generateObject({
      model: azure('gpt-4o-mini'),
      schema: EvaluationSchema,
      prompt: `
        Evaluate whether the retrieved information can answer this query:

        User Query: "${currentQuery}"

        Retrieved Chunks (${retrievedChunks.length} items):
        ${chunkContent}

        Retrieved Summaries (${retrievedSummaries.length} items):
        ${summaryContent}

        Current retry attempt: ${retryCount + 1} of ${maxRetries + 1}
        Has selected documents: ${hasSelectedDocuments}
        Current strategy: ${retrievalStrategy}
        Total retrieval attempts: ${state.retrievalAttempts}

        DECISION LOGIC:
        1. **generate_answer**: Use when content is relevant and sufficient to answer the query
        2. **refine_query**: Use when content is IRRELEVANT or completely unrelated to the query topic
        3. **retrieve_summary**: Use ONLY when content is somewhat related but lacks detail/context
        4. **fail_gracefully**: Use when max retries reached or no hope of finding relevant content

        CRITICAL: If the retrieved content is about a completely different topic than the query,
        choose "refine_query" to try different keywords, NOT "retrieve_summary".

        Example: Query about "Trump and China" but content about "app development" → choose "refine_query"

        SPECIAL CONSIDERATIONS:
        - If multiple retrieval attempts were made (retrievalAttempts >= 2), prefer refine_query over retrieve_summary
        - If this is the final retry attempt and information is insufficient, recommend fail_gracefully
        - Only use retrieve_summary if content is topically related but needs more context
      `,
    });

    const evaluation = result.object;
    let nextAction: NextAction;

    console.log(`🔍 AI Evaluation Details:`);
    console.log(`  - hasRelevantInfo: ${evaluation.hasRelevantInfo}`);
    console.log(`  - informationQuality: ${evaluation.informationQuality}`);
    console.log(`  - needsSummaryContext: ${evaluation.needsSummaryContext}`);
    console.log(`  - AI recommended action: ${evaluation.nextAction}`);
    console.log(`  - Reasoning: ${evaluation.reasoning}`);

    // Convert string to enum
    switch (evaluation.nextAction) {
      case 'generate_answer':
        nextAction = 'generate_answer' as NextAction;
        break;
      case 'retrieve_summary':
        nextAction = 'retrieve_summary' as NextAction;
        break;
      case 'refine_query':
        nextAction = 'refine_query' as NextAction;
        break;
      default:
        nextAction = 'fail_gracefully' as NextAction;
    }

    // Override if max retries reached
    if (retryCount >= maxRetries && evaluation.nextAction !== 'generate_answer') {
      console.log(`🚫 Overriding AI decision: Max retries (${retryCount}/${maxRetries}) reached`);
      nextAction = 'fail_gracefully' as NextAction;
    }

    console.log(`🎯 Final evaluation result: ${nextAction}`);

    return {
      reasoning: evaluation.reasoning,
      evaluationResult:
        nextAction === 'generate_answer'
          ? 'sufficient'
          : nextAction === 'retrieve_summary'
            ? 'need_summary'
            : nextAction === 'refine_query'
              ? 'need_refinement'
              : 'failed',
    };
  } catch (error) {
    console.error('❌ Error in result evaluation:', error);
    return {
      evaluationResult: 'failed',
      error: 'Failed to evaluate results',
    };
  }
}

/**
 * Node 4: Refine query and retry search
 */
export async function refineQuery(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('🔄 Refining query for better results...');

  // Emit step start event
  config?.writer?.({
    type: 'step_start',
    step: 'refine_query',
    message: 'Refining search strategy...',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    const { retryCount } = state;

    // Simulate query refinement logic
    const refinedKeywords = ['refined', 'improved', 'better'];
    const reasoning = `TESTING: Retry attempt ${retryCount + 1} - using more specific keywords`;

    console.log(`🧪 TESTING: Refined query with keywords: ${refinedKeywords.join(', ')}`);

    // Track testing keyword history
    const testKeywordHistory = [
      {
        attempt: retryCount + 1,
        keywords: refinedKeywords,
        strategy: 'chunks_only',
      },
    ];

    return {
      refinedKeywords,
      reasoning,
      currentQuery: `${state.originalQuery} (refined attempt ${retryCount + 1})`,
      retryCount: retryCount + 1,
      keywordHistory: testKeywordHistory,
      // Clear previous results for fresh search
      retrievedChunks: [],
      retrievedSummaries: [],
    };
  }

  try {
    const {
      originalQuery,
      currentQuery,
      retrievedChunks,
      retryCount,
      retrievalStrategy,
      refinedKeywords,
      keywordHistory,
    } = state;

    // Try different search strategy on retry
    let newRetrievalStrategy = retrievalStrategy;
    const nextRetryCount = retryCount + 1;

    if (nextRetryCount === 1 && retrievalStrategy === 'chunks_only') {
      // First retry: try full-text search instead of embedding (retryCount will become 1)
      console.log('🔄 Retry 1: Switching from embedding to full-text search');
      newRetrievalStrategy = 'chunks_only'; // Keep chunks but will use different search mode
    } else if (nextRetryCount === 2) {
      // Second retry: try summaries (retryCount will become 2)
      console.log('🔄 Retry 2: Switching to summary search');
      newRetrievalStrategy = 'summaries_only';
    }

    // Build history of previous attempts for AI context
    const previousAttempts = keywordHistory
      .map(
        (attempt) =>
          `Attempt ${attempt.attempt}: Keywords [${attempt.keywords.join(', ')}] with ${attempt.strategy} strategy`
      )
      .join('\n');

    const currentAttemptInfo =
      refinedKeywords.length > 0
        ? `Current Keywords: [${refinedKeywords.join(', ')}] with ${retrievalStrategy} strategy`
        : 'No current keywords set';

    // Analyze what went wrong and suggest better keywords
    const result = await generateObject({
      model: azure('gpt-4o-mini'),
      schema: QueryAnalysisSchema,
      prompt: `
        The previous search didn't return good results. Refine the search approach:

        Original Query: "${originalQuery}"
        Current Query: "${currentQuery}"
        Previous Results: ${retrievedChunks.length} chunks found
        Previous Strategy: ${retrievalStrategy}
        New Strategy: ${newRetrievalStrategy}

        PREVIOUS KEYWORD ATTEMPTS (AVOID REPEATING THESE):
        ${previousAttempts}
        ${currentAttemptInfo}

        This is retry attempt ${retryCount + 1}. Suggest:
        1. Alternative keywords that are DIFFERENT from previous attempts
        2. Different search terms or synonyms NOT used before
        3. Broader or more specific terms as needed
        4. Consider different aspects or angles of the query

        IMPORTANT: Generate keywords that are distinctly different from all previous attempts.
        Look at the query from a new perspective and use completely different terminology.
      `,
    });

    // Track this attempt in keyword history
    const newKeywordHistory = [
      {
        attempt: retryCount + 1,
        keywords: result.object.refinedKeywords,
        strategy: newRetrievalStrategy || 'chunks_only',
      },
    ];

    console.log(`🔄 Query refinement completed:`);
    console.log(`  - Previous keywords: [${refinedKeywords.join(', ')}]`);
    console.log(`  - New keywords: [${result.object.refinedKeywords.join(', ')}]`);
    console.log(`  - Strategy change: ${retrievalStrategy} → ${newRetrievalStrategy}`);
    console.log(`  - Retry count: ${retryCount} → ${retryCount + 1}`);

    return {
      currentQuery: result.object.refinedKeywords.join(' '),
      refinedKeywords: result.object.refinedKeywords,
      reasoning: result.object.reasoning,
      retrievalStrategy: newRetrievalStrategy,
      retryCount: retryCount + 1,
      keywordHistory: newKeywordHistory,
      // Clear previous results for fresh search
      retrievedChunks: [],
      retrievedSummaries: [],
    };
  } catch (error) {
    console.error('❌ Error in query refinement:', error);
    return {
      evaluationResult: 'failed',
      error: 'Failed to refine query',
    };
  }
}

/**
 * Node 5: Retrieve summary context (optimized for direct summary requests)
 */
export async function retrieveSummaryContext(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('📋 Retrieving summary context...');

  // Emit step start event
  config?.writer?.({
    type: 'step_start',
    step: 'retrieve_summary_context',
    message: 'Retrieving document summaries...',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    const mockSummaries: DocumentSummary[] = [
      {
        id: 'summary-1',
        fileName: 'Additional Summary 1.pdf',
        summary: `TESTING: Additional summary context for query: "${state.currentQuery}". This provides supplementary information to enhance the answer.`,
        similarity: 0.75,
        key: 'additional-key-1',
        fileType: 'pdf',
      },
    ];

    console.log(`🧪 TESTING: Retrieved ${mockSummaries.length} additional summaries`);

    return {
      retrievedSummaries: [...(state.retrievedSummaries || []), ...mockSummaries],
    };
  }

  try {
    const { currentQuery, config } = state;

    // Step 1: Generate keywords if not already available
    let keywords = state.refinedKeywords;
    if (!keywords || keywords.length === 0) {
      console.log('🔍 Generating keywords for summary retrieval...');

      const keywordResult = await generateObject({
        model: azure('gpt-4o-mini'),
        schema: QueryAnalysisSchema,
        prompt: `
          Generate effective search keywords for finding document summaries that contain information about this query:

          Query: "${currentQuery}"

          Focus on:
          - Main topics and concepts
          - Key entities (people, places, organizations)
          - Important terms that would appear in document summaries
          - Broader conceptual terms for summary-level matching

          Generate 3-4 keywords that would help find relevant document summaries.

          Also provide:
          - Query intent (factual, conceptual, comparative, procedural)
          - Retrieval strategy (for this case, use summaries_only)
          - Document count (3-5 for summary retrieval)
          - Reasoning for the keyword selection
        `,
      });

      keywords = keywordResult.object.refinedKeywords;
      console.log(`🎯 Generated keywords for summaries: ${keywords.join(', ')}`);
    }

    // Step 2: Search summaries using keywords (since we don't have summary embeddings yet)
    const summaries = await searchSummariesByKeywords(keywords, {
      limit: 5,
      spaceIds: config.spaceIds,
      documentIds: config.documentIds,
      userId: config.userId,
      debug: true,
    });

    console.log(`✅ Retrieved ${summaries.length} document summaries`);

    return {
      retrievedSummaries: summaries,
      refinedKeywords: keywords, // Update keywords if they were generated
      retrievalAttempts: 1, // Increment retrieval attempts
    };
  } catch (error) {
    console.error('❌ Error retrieving summary context:', error);
    return {};
  }
}

/**
 * Node 6: Generate final answer with citations
 */
export async function generateAnswer(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('✍️ Generating final answer...');

  // Handle document not found case
  if (state.planType === 'document_not_found') {
    console.log('📄 Handling document not found case - generating AI response');

    if (TESTING_MODE) {
      await simulateAIDelay();
      const testResponse = `I couldn't find the specific document you mentioned in your collection. You could upload it or search existing documents.`;

      // Stream the test response
      const words = testResponse.split(' ');
      for (let i = 0; i < words.length; i++) {
        const chunk = words[i] + (i < words.length - 1 ? ' ' : '');

        await config?.writer?.({
          type: 'content_chunk',
          content: chunk,
        });

        await new Promise((resolve) => setTimeout(resolve, 30));
      }

      return {
        finalAnswer: testResponse,
        references: [],
      };
    }

    // Use AI to generate a contextual response
    const result = await streamObject({
      model: azure('gpt-4o-mini'),
      schema: AnswerSchema,
      prompt: `
        The user asked for a specific document that doesn't exist in their collection.

        User Query: "${state.currentQuery}"

        Available Materials:
        ${state.targetMaterials?.map((m) => `- ${m.fileName}`).join('\n') || 'No materials available'}

        Generate a helpful response that:
        1. Politely explains that the specific document they asked for isn't in their collection
        2. Suggests practical next steps (upload document, check existing materials, etc.)
        3. Offers to help search their existing documents for related content
        4. Matches the language and tone of their original query
        5. Is concise but helpful

        Do not hallucinate or make up content about the requested document.
      `,
    });

    let fullAnswer = '';

    // Stream the AI response in real-time
    for await (const partialObject of result.partialObjectStream) {
      if (partialObject.answer) {
        const newContent = partialObject.answer.slice(fullAnswer.length);

        if (newContent) {
          // Emit each new chunk of content
          await config?.writer?.({
            type: 'content_chunk',
            content: newContent,
          });

          fullAnswer = partialObject.answer;
        }
      }
    }

    return {
      finalAnswer: fullAnswer,
      references: [],
    };
  }

  // Emit step start event
  config?.writer?.({
    type: 'step_start',
    step: 'generate_answer',
    message: 'Generating your answer...',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    const { currentQuery, retrievedChunks, retrievedSummaries } = state;
    const totalSources = retrievedChunks.length + retrievedSummaries.length;

    const testAnswer = `# 🧪 Test Response: ${currentQuery}

## Executive Summary

Based on **${totalSources} sources** from your document collection, here's a comprehensive analysis with rich markdown formatting to test the streaming renderer.

## Key Findings

### 1. Document Analysis Results
The system successfully retrieved and analyzed the following content:

- **Document chunks found**: ${retrievedChunks.length} relevant sections
- **Summary documents**: ${retrievedSummaries.length} high-level overviews
- **Retrieval strategy**: \`${state.retrievalStrategy}\`
- **Target materials**: ${state.targetMaterials?.length || 0} specifically selected

### 2. Content Quality Assessment

| Metric | Value | Status |
|--------|-------|--------|
| Relevance Score | 95% | ✅ Excellent |
| Coverage | 87% | ✅ Good |
| Confidence | 92% | ✅ High |

## Detailed Analysis

### Technical Implementation
The retrieval process used the following approach:

\`\`\`typescript
// Retrieval strategy implementation
const strategy = "${state.retrievalStrategy}";
const results = await searchDocuments({
  query: "${currentQuery}",
  strategy: strategy,
  limit: ${totalSources}
});
\`\`\`

### Source Distribution

1. **Primary Sources** (${Math.ceil(retrievedChunks.length * 0.7)} chunks)
   - High relevance content
   - Direct query matches
   - *Core information extracted*

2. **Supporting Sources** (${Math.floor(retrievedChunks.length * 0.3)} chunks)
   - Contextual information
   - Background details
   - **Supplementary insights**

3. **Summary Sources** (${retrievedSummaries.length} documents)
   - Document overviews
   - Key themes identified
   - \`Structured abstracts\`

## Recommendations

> **Note**: This is test content designed to verify markdown rendering during streaming.

### Next Steps
1. **Review the source materials** for additional context
2. **Ask follow-up questions** to dive deeper into specific topics
3. **Explore related documents** in your collection

### Advanced Features
- Use **bold text** for emphasis
- Apply *italic formatting* for nuance
- Include \`inline code\` for technical terms
- Create structured lists for clarity

---

**Confidence Level**: High (92%)
**Sources**: ${totalSources} documents analyzed
**Processing Time**: ~2.3 seconds

*This response demonstrates comprehensive markdown formatting during streaming to ensure proper rendering across all elements.*`;

    console.log('🧪 TESTING: Using enhanced markdown test answer');

    // Stream the response content in chunks
    const words = testAnswer.split(' ');
    for (let i = 0; i < words.length; i++) {
      const chunk = words[i] + (i < words.length - 1 ? ' ' : '');

      // Emit content chunk
      config?.writer?.({
        type: 'content_chunk',
        content: chunk,
      });

      // Small delay to simulate realistic streaming
      await new Promise((resolve) => setTimeout(resolve, 30));
    }

    console.log('🧪 TESTING: Generated final answer');

    // Convert MaterialSearchResult[] to RetrievedDocumentChunkDTO[]
    const references: RetrievedDocumentChunkDTO[] = retrievedChunks.map((chunk) => ({
      id: chunk.id,
      chunkId: chunk.chunkId,
      fileName: chunk.fileName,
      content: chunk.content,
      key: chunk.key,
      similarity: chunk.similarity,
      fileType: chunk.fileType,
      metadata: chunk.metadata as DocumentMetadata,
    }));

    return {
      finalAnswer: testAnswer,
      references,
    };
  }

  try {
    const { currentQuery, retrievedChunks, retrievedSummaries } = state;

    // Prepare source materials
    const sources = [
      ...retrievedChunks.map((chunk, idx) => ({
        index: idx,
        type: 'chunk',
        fileName: chunk.fileName,
        content: chunk.content,
        id: chunk.id,
        chunkId: chunk.chunkId,
        key: chunk.key,
        fileType: chunk.fileType,
        metadata: chunk.metadata,
      })),
      ...retrievedSummaries.map((summary, idx) => ({
        index: retrievedChunks.length + idx,
        type: 'summary',
        fileName: summary.fileName,
        content: summary.summary,
        id: summary.id,
        chunkId: 0,
        key: summary.key || '',
        fileType: DocumentType.PDF,
        metadata: {},
      })),
    ];

    const sourcesText = sources
      .map((source) => `[${source.index}] ${source.fileName} (${source.type}):\n${source.content}`)
      .join('\n\n');

    // Build enhanced context for material awareness - only if needed
    const needsContext = state.needSelectedContext;
    const hasSelectedContent =
      state.selectedContext?.documents?.length ||
      state.selectedContext?.spaces?.length ||
      state.config.documentIds?.length ||
      state.config.spaceIds?.length;

    const contextInfo = needsContext && hasSelectedContent ? await buildContextInfo(state) : '';
    const hasSpecificDocuments =
      state.selectedContext?.filterScope === 'documents' || state.config.documentIds?.length;

    // Use real AI streaming with enhanced material awareness
    const result = await streamObject({
      model: azure('gpt-4o-mini'),
      schema: AnswerSchema,
      prompt: `
        Answer the user's question using the provided sources. Include citations as footnotes.

        Question: "${currentQuery}"

        ${contextInfo}
        ${state.chatHistory ? state.chatHistory : ''}
        Sources:
        ${sourcesText}

        ENHANCED INSTRUCTIONS:
        1. Provide a comprehensive, accurate answer based on the sources
        2. Use information from multiple sources when possible
        3. For each claim or piece of information, add a citation like [1] referring to the source index
        4. Structure the answer clearly with proper formatting
        5. Only use information that is directly supported by the sources
        6. If sources don't fully answer the question, acknowledge the limitation

        ${
          hasSpecificDocuments
            ? `
        MATERIAL AWARENESS:
        - The user has selected specific documents for this query
        - When citing sources, mention the document names to help the user understand which materials provided the information
        - If the user asked for a summary or overview, focus on providing a comprehensive view from the selected materials
        `
            : ''
        }

        CITATION GUIDELINES:
        - Use [1], [2], etc. to reference sources by their index number
        - When mentioning specific documents, include both the citation and document name for clarity
        - Example: "According to the Python Programming Guide [1], functions are..."

        The citations array should map specific text to source indices for footnote generation.
      `,
    });

    let fullAnswer = '';

    // Stream the AI response in real-time
    for await (const partialObject of result.partialObjectStream) {
      if (partialObject.answer) {
        const newContent = partialObject.answer.slice(fullAnswer.length);

        if (newContent) {
          // Emit each new chunk of content
          await config?.writer?.({
            type: 'content_chunk',
            content: newContent,
          });

          fullAnswer = partialObject.answer;
        }
      }
    }

    // Get the final result
    const finalResult = await result.object;

    // Convert sources to references format for the response
    const references = sources.map((source) => ({
      id: source.id,
      chunkId: source.chunkId,
      fileName: source.fileName,
      content: source.content,
      key: source.key,
      similarity: 1,
      fileType: source.fileType,
      metadata: source.metadata,
    })) as RetrievedDocumentChunkDTO[];

    console.log('✅ Generated answer with real AI streaming and citations');

    return {
      finalAnswer: finalResult.answer,
      references,
    };
  } catch (error) {
    console.error('❌ Error generating answer:', error);

    // Fallback: generate simple answer without structured citations
    const fallbackAnswer = `I found some relevant information about "${state.currentQuery}", but encountered an error while generating a structured response. Please try rephrasing your question or check back later.`;

    // Stream the fallback answer
    const words = fallbackAnswer.split(' ');
    for (let i = 0; i < words.length; i++) {
      const chunk = words[i] + (i < words.length - 1 ? ' ' : '');

      // Emit content chunk
      await config?.writer?.({
        type: 'content_chunk',
        content: chunk,
      });

      // Small delay to simulate realistic streaming
      await new Promise((resolve) => setTimeout(resolve, 30));
    }

    return {
      finalAnswer: fallbackAnswer,
      error: 'Answer generation partially failed',
    };
  }
}

/**
 * Node 7: Handle failure cases gracefully
 */
export async function handleFailure(
  state: AgentStateType,
  config?: LangGraphRunnableConfig
): Promise<Partial<AgentStateType>> {
  console.log('🚫 Handling failure gracefully...');

  // Emit step start event
  config?.writer?.({
    type: 'step_start',
    step: 'handle_failure',
    message: 'Processing failed',
  });

  if (TESTING_MODE) {
    await simulateAIDelay();

    const { originalQuery, error } = state;
    const testFailureMessage = `🧪 TESTING: Simulated failure handling for query "${originalQuery}".

Error: ${error || 'Unknown error occurred'}

This is a test response showing how the system gracefully handles failures. In production, this would provide helpful guidance to users when searches don't return sufficient results.`;

    // Stream the response content in chunks
    const words = testFailureMessage.split(' ');
    for (let i = 0; i < words.length; i++) {
      const chunk = words[i] + (i < words.length - 1 ? ' ' : '');

      // Emit content chunk
      config?.writer?.({
        type: 'content_chunk',
        content: chunk,
      });

      // Small delay to simulate realistic streaming
      await new Promise((resolve) => setTimeout(resolve, 30));
    }

    console.log('🧪 TESTING: Handled failure gracefully');
    return {
      finalAnswer: testFailureMessage,
    };
  }

  const { originalQuery, error } = state;

  const fallbackAnswer = `I couldn't find sufficient information to answer your question about "${originalQuery}". This might be because:

1. The information isn't available in the current document collection
2. The question might need to be phrased differently
3. The topic might require documents that haven't been uploaded yet

You could try:
- Rephrasing your question with different keywords
- Being more specific or more general in your query
- Checking if relevant documents have been uploaded to your spaces

${error ? `\nTechnical details: ${error}` : ''}`;

  // Stream the failure response content in chunks
  const words = fallbackAnswer.split(' ');
  for (let i = 0; i < words.length; i++) {
    const chunk = words[i] + (i < words.length - 1 ? ' ' : '');

    // Emit content chunk
    await config?.writer?.({
      type: 'content_chunk',
      content: chunk,
    });

    // Small delay to simulate realistic streaming
    await new Promise((resolve) => setTimeout(resolve, 30));
  }

  return {
    finalAnswer: fallbackAnswer,
    evaluationResult: 'failed',
  };
}
