'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import type { AgentStep } from '@/hooks/use-chat-enhanced';
import type { DocumentSummary } from '@/lib/langgraph/types';
import { cn } from '@/lib/utils';
import { useReferenceStore } from '@/stores/reference';
import type { MaterialSearchResult } from '@/types/material';
import { DocumentType } from '@/types/material';
import {
  AlertCircle,
  BookOpen,
  Brain,
  ChevronDown,
  ChevronRight,
  Database,
  FileText,
  Lightbulb,
  Loader2,
  RefreshCw,
  Search,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { SummaryViewer } from './summary-viewer';

// Unified interface – represents each step and its rich data.
// Imported from hook so there's a single source of truth.

// Enhanced step interface for grouped display
interface GroupedStep {
  id: string;
  type: 'classify' | 'retrieve' | 'evaluate' | 'refine';
  title: string;
  status: 'completed' | 'running' | 'pending';
  message?: string;
  data: {
    keywords?: string[];
    reasoning?: string;
    retrievedChunks?: MaterialSearchResult[];
    retrievedSummaries?: DocumentSummary[];
    retrievalStrategy?: string;
    error?: string;
  };
  retrievalAttempt?: number; // For tracking multiple retrieval attempts
}

interface AgentProgressProps {
  steps: AgentStep[];
  isThinking: boolean;
  currentRunningStep?: {
    step: string;
    message: string;
  } | null;
  className?: string;
  autoExpanded?: boolean;
  onToggleDetails?: () => void;
}

export function AgentProgress({
  steps,
  isThinking,
  currentRunningStep,
  className,
  autoExpanded = false,
  onToggleDetails,
}: AgentProgressProps) {
  const [isExpanded, setIsExpanded] = useState(autoExpanded);
  const [selectedSummary, setSelectedSummary] = useState<DocumentSummary | null>(null);
  const { setSelectedReference } = useReferenceStore();

  // Auto-expand logic: expand when AI is working or when there are steps to show
  useEffect(() => {
    if (autoExpanded || isThinking || currentRunningStep || steps.length > 0) {
      setIsExpanded(true);
    }
  }, [autoExpanded, isThinking, currentRunningStep, steps.length]);

  // Handle chunk click - open document viewer
  const handleChunkClick = (chunk: MaterialSearchResult) => {
    const referenceData = {
      id: chunk.id,
      chunkId: chunk.chunkId,
      fileName: chunk.fileName,
      content: chunk.content,
      key: chunk.key,
      similarity: chunk.similarity,
      fileType: chunk.fileType,
      metadata: {
        pageNumber: (chunk.metadata as { pageNumber?: number })?.pageNumber,
        timestamp: Date.now(),
      },
    };
    setSelectedReference(referenceData);
  };

  // Handle summary click - open summary viewer
  const handleSummaryClick = (summary: DocumentSummary) => {
    setSelectedSummary(summary);
  };

  // Convert raw steps into grouped, user-friendly steps
  const groupSteps = (
    rawSteps: AgentStep[],
    currentStep?: { step: string; message: string } | null
  ): GroupedStep[] => {
    const grouped: GroupedStep[] = [];

    // Track what we've seen
    let hasClassifyStep = false;
    let retrievalAttemptCount = 0;
    let evaluationAttemptCount = 0;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let currentRetrievalData: any = {};

    // Process completed steps
    for (const step of rawSteps) {
      switch (step.step) {
        case 'classify_query':
          if (!hasClassifyStep) {
            grouped.push({
              id: 'classify',
              type: 'classify',
              title: 'Understanding your question',
              status: 'completed',
              data: {
                reasoning: step.reasoning,
              },
            });
            hasClassifyStep = true;
          }
          break;

        case 'create_research_plan':
        case 'resolve_material_context':
        case 'retrieve_context':
        case 'retrieve_summary_context':
          // Accumulate retrieval data
          if (step.extractedKeywords) currentRetrievalData.keywords = step.extractedKeywords;
          if (step.reasoning) currentRetrievalData.reasoning = step.reasoning;
          if (step.retrievedChunks) currentRetrievalData.retrievedChunks = step.retrievedChunks;
          if (step.retrievedSummaries)
            currentRetrievalData.retrievedSummaries = step.retrievedSummaries;
          if (step.retrievalStrategy)
            currentRetrievalData.retrievalStrategy = step.retrievalStrategy;
          if (step.error) currentRetrievalData.error = step.error;

          // If this is a retrieval completion step, always create a new retrieval step
          if (step.step === 'retrieve_context' || step.step === 'retrieve_summary_context') {
            retrievalAttemptCount++;

            // Determine the title based on attempt count and step type
            const getRetrievalTitle = () => {
              if (retrievalAttemptCount === 1) {
                return 'Searching for context';
              } else {
                const stepType = step.step === 'retrieve_summary_context' ? 'summaries' : 'context';
                return `Searching for ${stepType}`;
              }
            };

            // Always create a new retrieval step for each attempt
            grouped.push({
              id: `retrieve-${retrievalAttemptCount}`,
              type: 'retrieve',
              title: getRetrievalTitle(),
              status: 'completed',
              data: { ...currentRetrievalData },
              retrievalAttempt: retrievalAttemptCount,
            });

            // Reset retrieval data for next attempt
            currentRetrievalData = {};
          }
          break;

        case 'evaluate_results':
          evaluationAttemptCount++;

          // Always create a new evaluation step for each attempt
          const getEvaluationTitle = () => {
            return 'Evaluating retrieved context';
          };

          grouped.push({
            id: `evaluate-${evaluationAttemptCount}`,
            type: 'evaluate',
            title: getEvaluationTitle(),
            status: 'completed',
            data: {
              reasoning: step.reasoning,
            },
          });
          break;

        case 'refine_query':
          // Always use the same title for refine steps
          const refineTitle = 'Refining search approach';

          grouped.push({
            id: `refine-${grouped.length}`,
            type: 'refine',
            title: refineTitle,
            status: 'completed',
            data: {
              reasoning: step.reasoning,
              keywords: step.extractedKeywords,
            },
          });
          // Reset retrieval data for next attempt
          currentRetrievalData = {};
          break;
      }
    }

    // Handle current running step
    if (currentStep) {
      const isRetrievalStep = [
        'create_research_plan',
        'resolve_material_context',
        'retrieve_context',
        'retrieve_summary_context',
      ].includes(currentStep.step);

      if (currentStep.step === 'classify_query' && !hasClassifyStep) {
        grouped.push({
          id: 'classify-current',
          type: 'classify',
          title: 'Understanding your question',
          status: 'running',
          message: currentStep.message,
          data: {},
        });
      } else if (isRetrievalStep) {
        // Always create a new running retrieval step
        const currentAttempt = retrievalAttemptCount + 1;
        const runningTitle = 'Searching for context';

        grouped.push({
          id: `retrieve-current-${currentAttempt}`,
          type: 'retrieve',
          title: runningTitle,
          status: 'running',
          message: currentStep.message,
          data: { ...currentRetrievalData },
          retrievalAttempt: currentAttempt,
        });
      } else if (currentStep.step === 'evaluate_results') {
        // Always create a new running evaluation step
        const currentEvalAttempt = evaluationAttemptCount + 1;
        const evalTitle = 'Evaluating retrieved context';

        grouped.push({
          id: `evaluate-current-${currentEvalAttempt}`,
          type: 'evaluate',
          title: evalTitle,
          status: 'running',
          message: currentStep.message,
          data: {},
        });
      }
    }

    return grouped;
  };

  const getFileIcon = (fileType: DocumentType | string) => {
    switch (fileType) {
      case DocumentType.PDF:
        return '📄';
      case DocumentType.DOCX:
        return '📝';
      case DocumentType.TXT:
        return '📄';
      case DocumentType.OTHER:
        return '📄';
      default:
        return '📄';
    }
  };

  // Get grouped steps for display
  const groupedSteps = groupSteps(steps, currentRunningStep);

  // Get icon for step type
  const getStepIcon = (type: GroupedStep['type'], status: GroupedStep['status']) => {
    if (status === 'running') {
      return <Loader2 className="h-3 w-3 animate-spin text-blue-500" />;
    }

    switch (type) {
      case 'classify':
        return <Brain className="h-3 w-3 text-purple-500" />;
      case 'retrieve':
        return <Search className="h-3 w-3 text-blue-500" />;
      case 'evaluate':
        return <Database className="h-3 w-3 text-green-500" />;
      case 'refine':
        return <RefreshCw className="h-3 w-3 text-orange-500" />;
      default:
        return <div className="h-3 w-3 rounded-full bg-gray-400" />;
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    onToggleDetails?.();
  };

  // Don't show if there are no steps and not currently thinking
  if (!isThinking && !currentRunningStep && !groupedSteps.length) {
    return null;
  }

  // Get current step title for header
  const getCurrentStepTitle = () => {
    const runningStep = groupedSteps.find((step) => step.status === 'running');
    return runningStep?.title || 'Processing...';
  };

  return (
    <div
      className={cn(
        'bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20',
        'border border-blue-200 dark:border-blue-800',
        'mb-2 rounded-lg p-2',
        'transition-all duration-300 ease-in-out',
        'shadow-md',
        className
      )}
    >
      {/* Header with current step indicator */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Brain className="h-4 w-4 text-blue-500" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            AI Thinking Process
          </span>
          {currentRunningStep && (
            <div className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>{getCurrentStepTitle()}</span>
            </div>
          )}
        </div>

        {groupedSteps.length > 0 && (
          <Button variant="ghost" size="sm" onClick={toggleExpanded} className="h-8 px-2 text-xs">
            {isExpanded ? (
              <>
                <ChevronDown className="mr-1 h-3 w-3" />
                Hide Details
              </>
            ) : (
              <>
                <ChevronRight className="mr-1 h-3 w-3" />
                Show Details
              </>
            )}
          </Button>
        )}
      </div>

      {/* Expandable Detailed Steps */}
      {isExpanded && (
        <div className="mt-1.5 space-y-2 border-t border-blue-200 pt-2 dark:border-blue-800">
          {/* Grouped steps */}
          {groupedSteps.map((groupedStep, index) => {
            return (
              <div
                key={groupedStep.id}
                className={cn(
                  'rounded-lg border bg-white p-1.5 px-2 dark:border-gray-700 dark:bg-gray-800',
                  'transition-all duration-200',
                  groupedStep.status === 'running' && 'ring-1 ring-blue-300 dark:ring-blue-600'
                )}
              >
                {/* Step Header */}
                <div className="mb-1.5 flex items-center space-x-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-xs text-gray-600">
                      {String(index + 1).padStart(2, '0')}
                    </span>
                    {/* Status indicator */}
                    {groupedStep.status === 'completed' ? (
                      <div className="flex h-3 w-3 items-center justify-center rounded-full bg-green-500">
                        <div className="h-1.5 w-1.5 rounded-full bg-white"></div>
                      </div>
                    ) : (
                      getStepIcon(groupedStep.type, groupedStep.status)
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{groupedStep.title}</span>

                    </div>
                    {groupedStep.status === 'running' && groupedStep.message && (
                      <div className="text-xs text-blue-600 dark:text-blue-400">
                        {groupedStep.message}
                      </div>
                    )}
                  </div>
                </div>

                {/* Step Details */}
                <div className="ml-5 space-y-2 text-sm">
                  {/* For classify: Show reasoning */}
                  {groupedStep.type === 'classify' && groupedStep.data.reasoning && (
                    <div>
                      <p className="flex items-start gap-1 rounded bg-slate-100 p-2 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                        <Lightbulb className="h-3.5 w-3.5 shrink-0 text-yellow-500" />
                        {groupedStep.data.reasoning}
                      </p>
                    </div>
                  )}

                  {/* For retrieve: Show keywords, chunks, and summaries */}
                  {groupedStep.type === 'retrieve' && (
                    <>
                      {/* Show keywords if available */}
                      {groupedStep.data.keywords && groupedStep.data.keywords.length > 0 && (
                        <div>
                          <div className="mb-1 flex items-center gap-2">
                            <span className="text-xs text-gray-600 dark:text-gray-400">
                              Key terms
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {groupedStep.data.keywords.map((keyword, idx) => (
                              <span
                                key={idx}
                                className="flex items-center gap-1 rounded bg-slate-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                              >
                                <Search className="h-3 w-3 shrink-0 text-slate-400" />
                                {keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Show retrieval strategy */}
                      {groupedStep.data.retrievalStrategy && (
                        <div>
                          <div className="mb-1 flex items-center gap-2">
                            <Database className="h-3 w-3 text-purple-500" />
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                              Strategy: {groupedStep.data.retrievalStrategy.replace(/_/g, ' ')}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Show retrieved chunks */}
                      {groupedStep.data.retrievedChunks && (
                        <div>
                          <div className="mb-1 flex items-center gap-2">
                            <FileText className="h-3 w-3 text-blue-500" />
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                              Found {groupedStep.data.retrievedChunks.length} relevant document
                              chunks
                            </span>
                          </div>
                          {groupedStep.data.retrievedChunks.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {groupedStep.data.retrievedChunks
                                .slice(0, 3)
                                .map((chunk: MaterialSearchResult, idx: number) => (
                                  <div
                                    key={idx}
                                    className="flex cursor-pointer items-center gap-1 rounded-md border border-slate-300 bg-slate-100 px-2 py-1 text-xs text-gray-600 transition-colors hover:bg-gray-200 dark:border-slate-700 dark:bg-slate-800 dark:text-gray-400"
                                    title={`Click to view ${chunk.fileName}`}
                                    onClick={() => handleChunkClick(chunk)}
                                  >
                                    <span className="text-xs">{getFileIcon(chunk.fileType)}</span>
                                    <span className="truncate">
                                      {chunk.fileName.length > 20
                                        ? chunk.fileName.substring(0, 20) + '...'
                                        : chunk.fileName}
                                    </span>
                                  </div>
                                ))}
                              {groupedStep.data.retrievedChunks.length > 3 && (
                                <div className="text-xs text-gray-500">
                                  +{groupedStep.data.retrievedChunks.length - 3} more chunks
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}

                      {/* Show retrieved summaries */}
                      {groupedStep.data.retrievedSummaries && (
                        <div className="mt-2">
                          <div className="mb-1 flex items-center gap-2">
                            <BookOpen className="h-3 w-3 text-indigo-500" />
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                              Found {groupedStep.data.retrievedSummaries.length} document
                              summaries
                            </span>
                          </div>
                          {groupedStep.data.retrievedSummaries.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {groupedStep.data.retrievedSummaries
                                .slice(0, 3)
                                .map((summary: DocumentSummary, idx: number) => (
                                  <div
                                    key={idx}
                                    className="flex cursor-pointer items-center gap-1 rounded-md border border-indigo-300 bg-indigo-50 px-2 py-1 text-xs text-indigo-700 transition-colors hover:bg-indigo-100 dark:border-indigo-700 dark:bg-indigo-950 dark:text-indigo-300"
                                    title={`Click to view summary of ${summary.fileName}`}
                                    onClick={() => handleSummaryClick(summary)}
                                  >
                                    <BookOpen className="h-3 w-3 shrink-0" />
                                    <span className="truncate">
                                      {summary.fileName.length > 20
                                        ? summary.fileName.substring(0, 20) + '...'
                                        : summary.fileName}
                                    </span>
                                  </div>
                                ))}
                              {groupedStep.data.retrievedSummaries.length > 3 && (
                                <div className="text-xs text-gray-500">
                                  +{groupedStep.data.retrievedSummaries.length - 3} more summaries
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </>
                  )}

                  {/* For evaluate: Show reasoning */}
                  {groupedStep.type === 'evaluate' && groupedStep.data.reasoning && (
                    <div>
                      <p className="flex items-start gap-1 rounded bg-slate-100 p-2 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                        <Lightbulb className="h-3.5 w-3.5 shrink-0 text-blue-500" />
                        {groupedStep.data.reasoning}
                      </p>
                    </div>
                  )}

                  {/* For refine: Show reasoning and keywords */}
                  {groupedStep.type === 'refine' && (
                    <>
                      {groupedStep.data.reasoning && (
                        <div>
                          <div className="mb-1 flex items-center gap-2">
                            <RefreshCw className="h-3 w-3 text-orange-500" />
                            <span className="text-xs font-medium">Refinement:</span>
                          </div>
                          <p className="rounded bg-gray-100 p-2 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                            {groupedStep.data.reasoning}
                          </p>
                        </div>
                      )}
                      {groupedStep.data.keywords && groupedStep.data.keywords.length > 0 && (
                        <div className="mt-2">
                          <div className="mb-1 flex items-center gap-2">
                            <span className="text-xs text-gray-600 dark:text-gray-400">
                              New key terms
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {groupedStep.data.keywords.map((keyword, idx) => (
                              <span
                                key={idx}
                                className="flex items-center gap-1 rounded bg-orange-100 px-2 py-1 text-xs text-orange-700 dark:bg-orange-900 dark:text-orange-300"
                              >
                                <Search className="h-3 w-3 shrink-0 text-orange-400" />
                                {keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  )}

                  {/* Error Messages (for any step) */}
                  {groupedStep.data.error && (
                    <div>
                      <div className="mb-1 flex items-center gap-2">
                        <AlertCircle className="h-3 w-3 text-red-500" />
                        <span className="text-xs font-medium text-red-600">Error:</span>
                      </div>
                      <p className="rounded bg-red-50 p-2 text-xs text-red-600 dark:bg-red-950/20">
                        {groupedStep.data.error}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Summary Viewer Dialog */}
      <SummaryViewer
        summary={selectedSummary}
        isOpen={!!selectedSummary}
        onClose={() => setSelectedSummary(null)}
      />
    </div>
  );
}
