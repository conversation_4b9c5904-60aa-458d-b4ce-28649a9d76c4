'use client';

import { But<PERSON> } from '@/components/ui/button';
import type { AgentStep } from '@/hooks/use-chat-enhanced';
import type { DocumentSummary } from '@/lib/langgraph/types';
import { cn } from '@/lib/utils';
import { useReferenceStore } from '@/stores/reference';
import type { MaterialSearchResult } from '@/types/material';
import {
  AlertCircle,
  Brain,
  ChevronDown,
  ChevronRight,
  Database,
  FileText,
  Lightbulb,
  Loader2,
  RefreshCw,
  Search,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { SummaryViewer } from './summary-viewer';

interface AgentProgressProps {
  steps: AgentStep[];
  isThinking: boolean;
  currentRunningStep?: {
    step: string;
    message: string;
  } | null;
  className?: string;
  autoExpanded?: boolean;
  onToggleDetails?: () => void;
}

export function AgentProgress({
  steps,
  isThinking,
  currentRunningStep,
  className,
  autoExpanded = false,
  onToggleDetails,
}: AgentProgressProps) {
  const [isExpanded, setIsExpanded] = useState(autoExpanded);
  const [selectedSummary, setSelectedSummary] = useState<DocumentSummary | null>(null);
  const { setSelectedReference } = useReferenceStore();

  // Auto-expand logic: expand when AI is working or when there are steps to show
  useEffect(() => {
    if (autoExpanded || isThinking || currentRunningStep || steps.length > 0) {
      setIsExpanded(true);
    }
  }, [autoExpanded, isThinking, currentRunningStep, steps.length]);

  // Handle chunk click - open document viewer
  const handleChunkClick = (chunk: MaterialSearchResult) => {
    const referenceData = {
      id: chunk.id,
      chunkId: chunk.chunkId,
      fileName: chunk.fileName,
      content: chunk.content,
      key: chunk.key,
      similarity: chunk.similarity,
      fileType: chunk.fileType,
      metadata: {
        pageNumber: (chunk.metadata as { pageNumber?: number })?.pageNumber,
        timestamp: Date.now(),
      },
    };
    setSelectedReference(referenceData);
  };

  // Handle summary click - open summary viewer
  const handleSummaryClick = (summary: DocumentSummary) => {
    setSelectedSummary(summary);
  };

  // Simple step processing - no complex grouping, just display what we have
  const getDisplaySteps = () => {
    const displaySteps: Array<{
      id: string;
      title: string;
      status: 'completed' | 'running';
      message?: string;
      data?: AgentStep;
      stepType: string;
    }> = [];

    // Process completed steps - use Set to track unique step types to prevent duplicates
    const seenSteps = new Set<string>();
    
    steps.forEach((step, index) => {
      // Skip hidden internal steps
      if (['create_research_plan', 'resolve_material_context'].includes(step.step)) {
        return;
      }

      // Create unique key for this step to prevent duplicates
      const stepKey = `${step.step}-${step.stepId || index}`;
      
      // Skip if we've already seen this exact step
      if (seenSteps.has(stepKey)) {
        return;
      }
      seenSteps.add(stepKey);

      const title = getStepTitle(step.step);
      if (title) {
        displaySteps.push({
          id: stepKey,
          title,
          status: 'completed',
          data: step,
          stepType: step.step,
        });
      }
    });

    // Add current running step if it exists and isn't hidden
    if (currentRunningStep && !['create_research_plan', 'resolve_material_context'].includes(currentRunningStep.step)) {
      const title = getStepTitle(currentRunningStep.step);
      if (title) {
        const runningStepKey = `running-${currentRunningStep.step}`;

        // Check if we already have this step type (either completed or running)
        const existingStepIndex = displaySteps.findIndex(s => s.stepType === currentRunningStep.step);

        if (existingStepIndex >= 0) {
          // Replace the existing step with the running version
          displaySteps[existingStepIndex] = {
            id: runningStepKey,
            title,
            status: 'running',
            message: currentRunningStep.message,
            stepType: currentRunningStep.step,
          };
        } else {
          // Add new running step if no existing step found
          displaySteps.push({
            id: runningStepKey,
            title,
            status: 'running',
            message: currentRunningStep.message,
            stepType: currentRunningStep.step,
          });
        }
      }
    }

    return displaySteps;
  };

  // Simple title mapping
  const getStepTitle = (stepType: string): string | null => {
    switch (stepType) {
      case 'classify_query':
        return 'Understanding your question';
      case 'retrieve_context':
      case 'retrieve_summary_context':
        return 'Searching for context';
      case 'evaluate_results':
        return 'Evaluating retrieved context';
      case 'refine_query':
        return 'Refining search approach';
      case 'generate_answer':
        return 'Generating answer';
      default:
        return null;
    }
  };

  // Get icon for step type
  const getStepIcon = (stepType: string, status: 'completed' | 'running') => {
    if (status === 'running') {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    }

    switch (stepType) {
      case 'classify_query':
        return <Brain className="h-4 w-4 text-green-500" />;
      case 'retrieve_context':
      case 'retrieve_summary_context':
        return <Search className="h-4 w-4 text-green-500" />;
      case 'evaluate_results':
        return <AlertCircle className="h-4 w-4 text-green-500" />;
      case 'refine_query':
        return <RefreshCw className="h-4 w-4 text-green-500" />;
      case 'generate_answer':
        return <Lightbulb className="h-4 w-4 text-green-500" />;
      default:
        return <FileText className="h-4 w-4 text-green-500" />;
    }
  };

  const displaySteps = getDisplaySteps();
  const currentStepTitle = displaySteps.find(s => s.status === 'running')?.title || 'Processing...';

  // Don't show if no steps and not thinking
  if (displaySteps.length === 0 && !isThinking && !currentRunningStep) {
    return null;
  }

  return (
    <div className={cn('rounded-lg border bg-card text-card-foreground shadow-sm', className)}>
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-2">
          <Database className="h-5 w-5 text-blue-500" />
          <h3 className="text-sm font-medium">AI Thinking Process</h3>
          {(isThinking || currentRunningStep) && (
            <span className="text-xs text-muted-foreground">
              {currentStepTitle}
            </span>
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setIsExpanded(!isExpanded);
            onToggleDetails?.();
          }}
          className="h-8 w-8 p-0"
        >
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      </div>

      {isExpanded && (
        <div className="border-t px-4 pb-4">
          <div className="space-y-3 pt-3">
            {displaySteps.map((step, index) => (
              <div key={step.id} className="flex items-start space-x-3">
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-muted text-xs font-medium">
                  {String(index + 1).padStart(2, '0')}
                </div>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center space-x-2">
                    {getStepIcon(step.stepType, step.status)}
                    <span className="text-sm font-medium">{step.title}</span>
                  </div>
                  {step.status === 'running' && step.message && (
                    <p className="text-xs text-muted-foreground">{step.message}</p>
                  )}
                  {step.data && step.status === 'completed' && (
                    <div className="space-y-2">
                      {step.data.reasoning && (
                        <p className="text-xs text-muted-foreground">{step.data.reasoning}</p>
                      )}

                      {/* Keywords */}
                      {step.data.extractedKeywords && step.data.extractedKeywords.length > 0 && (
                        <div className="space-y-1">
                          <p className="text-xs font-medium text-muted-foreground">Key terms</p>
                          <div className="flex flex-wrap gap-1">
                            {step.data.extractedKeywords.map((keyword, idx) => (
                              <span
                                key={idx}
                                className="inline-flex items-center rounded-full bg-orange-100 px-2 py-1 text-xs text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                              >
                                {keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Retrieved Chunks */}
                      {step.data.retrievedChunks && step.data.retrievedChunks.length > 0 && (
                        <div className="space-y-1">
                          <p className="text-xs font-medium text-muted-foreground">
                            📄 Found {step.data.retrievedChunks.length} relevant document chunks
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {step.data.retrievedChunks.slice(0, 3).map((chunk, idx) => (
                              <button
                                key={idx}
                                onClick={() => handleChunkClick(chunk as MaterialSearchResult)}
                                className="inline-flex items-center rounded bg-blue-100 px-2 py-1 text-xs text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:hover:bg-blue-800"
                              >
                                📄 {chunk.fileName.length > 20 ? `${chunk.fileName.substring(0, 20)}...` : chunk.fileName}
                              </button>
                            ))}
                            {step.data.retrievedChunks.length > 3 && (
                              <span className="inline-flex items-center rounded bg-gray-100 px-2 py-1 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                                +{step.data.retrievedChunks.length - 3} more chunks
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Retrieved Summaries */}
                      {step.data.retrievedSummaries && step.data.retrievedSummaries.length > 0 && (
                        <div className="space-y-1">
                          <p className="text-xs font-medium text-muted-foreground">
                            📋 Found {step.data.retrievedSummaries.length} document summaries
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {step.data.retrievedSummaries.map((summary, idx) => (
                              <button
                                key={idx}
                                onClick={() => handleSummaryClick(summary)}
                                className="inline-flex items-center rounded bg-green-100 px-2 py-1 text-xs text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-200 dark:hover:bg-green-800"
                              >
                                📋 {summary.fileName.length > 20 ? `${summary.fileName.substring(0, 20)}...` : summary.fileName}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Error */}
                      {step.data.error && (
                        <div className="rounded-md bg-red-50 p-2 dark:bg-red-900/20">
                          <p className="text-xs text-red-600 dark:text-red-400">{step.data.error}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {selectedSummary && (
        <SummaryViewer
          isOpen={true}
          summary={selectedSummary}
          onClose={() => setSelectedSummary(null)}
        />
      )}
    </div>
  );
}

export default AgentProgress;
