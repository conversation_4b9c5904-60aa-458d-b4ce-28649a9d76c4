{"name": "knowledge-sphere", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "check-types": "tsc --noEmit", "lint": "npm run check-types && next lint --dir src", "prepare": "husky", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@ai-sdk/azure": "^1.3.23", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.22", "@aws-sdk/client-s3": "^3.670.0", "@aws-sdk/s3-request-presigner": "^3.670.0", "@azure/identity": "^4.5.0", "@bprogress/next": "^3.2.12", "@cyntler/react-doc-viewer": "^1.17.0", "@hookform/resolvers": "^3.9.0", "@langchain/community": "^0.3.6", "@langchain/core": "^0.3.13", "@langchain/langgraph": "^0.3.1", "@langchain/langgraph-sdk": "^0.0.84", "@lucia-auth/adapter-drizzle": "^1.1.0", "@node-rs/argon2": "^2.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf-viewer/core": "3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/page-navigation": "^3.12.0", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-query": "^5.59.15", "@trpc/client": "11.0.0-rc.584", "@trpc/react-query": "11.0.0-rc.584", "@trpc/server": "11.0.0-rc.584", "ai": "^4.3.16", "antd": "^5.21.3", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "client-only": "^0.0.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.34.1", "langchain": "^0.3.2", "lucia": "^3.2.1", "lucide-react": "^0.453.0", "luxon": "^3.5.0", "mammoth": "^1.8.0", "next": "14.2.30", "next-themes": "^0.3.0", "openai": "^4.67.3", "pdf-parse": "^1.1.1", "pdfjs-dist": "3.4.120", "pg": "^8.13.0", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.2.10", "react-hook-form": "^7.53.1", "react-markdown": "^9.0.3", "remark-gfm": "^4.0.1", "resend": "^4.3.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "superjson": "^2.2.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "uuid": "^10.0.0", "zod": "^3.23.8", "zod-form-data": "^2.0.2", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@ianvs/prettier-plugin-sort-imports": "^4.3.1", "@tailwindcss/typography": "^0.5.16", "@types/luxon": "^3.4.2", "@types/node": "^20", "@types/pg": "^8.11.10", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "dotenv-expand": "^11.0.6", "drizzle-kit": "^0.25.0", "eslint": "^8", "eslint-config-next": "14.2.15", "eslint-config-prettier": "^9.1.0", "husky": "^9.1.6", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "pretty-quick": "^4.0.0", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "~5.5.0"}}